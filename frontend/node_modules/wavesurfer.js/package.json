{"name": "wavesurfer.js", "version": "7.9.5", "license": "BSD-3-<PERSON><PERSON>", "author": "<PERSON><PERSON><PERSON><PERSON>", "description": "Audio waveform player", "homepage": "https://wavesurfer.xyz", "keywords": ["waveform", "spectrogram", "audio", "player", "music", "linguistics"], "repository": {"type": "git", "url": "**************:katspaugh/wavesurfer.js.git"}, "type": "module", "files": ["dist"], "main": "./dist/wavesurfer.js", "unpkg": "./dist/wavesurfer.min.js", "module": "./dist/wavesurfer.js", "browser": "./dist/wavesurfer.js", "types": "./dist/wavesurfer.d.ts", "exports": {".": {"import": "./dist/wavesurfer.esm.js", "types": "./dist/wavesurfer.d.ts", "require": "./dist/wavesurfer.cjs"}, "./dist/plugins/*.js": {"import": "./dist/plugins/*.esm.js", "types": "./dist/plugins/*.d.ts", "require": "./dist/plugins/*.cjs"}, "./plugins/*": {"import": "./dist/plugins/*.esm.js", "types": "./dist/plugins/*.d.ts", "require": "./dist/plugins/*.cjs"}, "./dist/*": {"import": "./dist/*", "types": "./dist/*.d.ts", "require": "./dist/*.cjs"}, "./dist/plugins/*.esm.js": {"import": "./dist/plugins/*.esm.js", "types": "./dist/plugins/*.d.ts", "require": "./dist/plugins/*.cjs"}}, "scripts": {"clean": "node ./scripts/clean.cjs", "build:dev": "tsc -w --target ESNext", "build": "npm run clean && tsc && rollup -c", "prepublishOnly": "npm run build", "lint": "eslint --ext .ts src --fix", "lint:report": "eslint --output-file eslint_report.json --format json --ext .ts src", "prettier": "prettier -w '**/*.{js,ts,css}' --ignore-path .gitignore", "make-plugin": "./scripts/plugin.sh", "cypress": "cypress open --e2e", "cypress:canary": "cypress open --e2e -b chrome:canary", "test": "cypress run --browser chrome", "serve": "npx live-server --port=9090 --no-browser --ignore='.*,src,cypress,scripts'", "start": "npm run build:dev & npm run serve"}, "packageManager": "yarn@1.22.22", "devDependencies": {"@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^12.1.1", "@typescript-eslint/eslint-plugin": "^8.18.0", "@typescript-eslint/parser": "^8.18.0", "cypress": "^13.16.1", "cypress-image-snapshot": "^4.0.1", "eslint": "^9.17.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "glob": "^11.0.0", "prettier": "^3.4.2", "rollup": "^4.28.1", "rollup-plugin-dts": "^6.1.0", "typescript": "^5.7.2"}}