import EventEmitter from './event-emitter.js';
import type { WaveSurferOptions } from './wavesurfer.js';
type RendererEvents = {
    click: [relativeX: number, relativeY: number];
    dblclick: [relativeX: number, relativeY: number];
    drag: [relativeX: number];
    dragstart: [relativeX: number];
    dragend: [relativeX: number];
    scroll: [relativeStart: number, relativeEnd: number, scrollLeft: number, scrollRight: number];
    render: [];
    rendered: [];
};
declare class Renderer extends EventEmitter<RendererEvents> {
    private static MAX_CANVAS_WIDTH;
    private static MAX_NODES;
    private options;
    private parent;
    private container;
    private scrollContainer;
    private wrapper;
    private canvasWrapper;
    private progressWrapper;
    private cursor;
    private timeouts;
    private isScrollable;
    private audioData;
    private resizeObserver;
    private lastContainerWidth;
    private isDragging;
    private subscriptions;
    private unsubscribeOnScroll;
    constructor(options: WaveSurferOptions, audioElement?: HTMLElement);
    private parentFromOptionsContainer;
    private initEvents;
    private onContainerResize;
    private initDrag;
    private getHeight;
    private initHtml;
    /** Wavesurfer itself calls this method. Do not call it manually. */
    setOptions(options: WaveSurferOptions): void;
    getWrapper(): HTMLElement;
    getWidth(): number;
    getScroll(): number;
    setScroll(pixels: number): void;
    setScrollPercentage(percent: number): void;
    destroy(): void;
    private createDelay;
    private convertColorValues;
    private getPixelRatio;
    private renderBarWaveform;
    private renderLineWaveform;
    private renderWaveform;
    private renderSingleCanvas;
    private renderMultiCanvas;
    private renderChannel;
    render(audioData: AudioBuffer): Promise<void>;
    reRender(): void;
    zoom(minPxPerSec: number): void;
    private scrollIntoView;
    renderProgress(progress: number, isPlaying?: boolean): void;
    exportImage(format: string, quality: number, type: 'dataURL' | 'blob'): Promise<string[] | Blob[]>;
}
export default Renderer;
