!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):((e="undefined"!=typeof globalThis?globalThis:e||self).WaveSurfer=e.WaveSurfer||{},e.WaveSurfer.Hover=t())}(this,(function(){"use strict";class e{constructor(){this.listeners={}}on(e,t,s){if(this.listeners[e]||(this.listeners[e]=new Set),this.listeners[e].add(t),null==s?void 0:s.once){const s=()=>{this.un(e,s),this.un(e,t)};return this.on(e,s),s}return()=>this.un(e,t)}un(e,t){var s;null===(s=this.listeners[e])||void 0===s||s.delete(t)}once(e,t){return this.on(e,t,{once:!0})}unAll(){this.listeners={}}emit(e,...t){this.listeners[e]&&this.listeners[e].forEach((e=>e(...t)))}}class t extends e{constructor(e){super(),this.subscriptions=[],this.options=e}onInit(){}_init(e){this.wavesurfer=e,this.onInit()}destroy(){this.emit("destroy"),this.subscriptions.forEach((e=>e()))}}function s(e,t){const i=t.xmlns?document.createElementNS(t.xmlns,e):document.createElement(e);for(const[e,n]of Object.entries(t))if("children"===e)for(const[e,n]of Object.entries(t))"string"==typeof n?i.appendChild(document.createTextNode(n)):i.appendChild(s(e,n));else"style"===e?Object.assign(i.style,n):"textContent"===e?i.textContent=n:i.setAttribute(e,n.toString());return i}function i(e,t,i){const n=s(e,t||{});return null==i||i.appendChild(n),n}const n={lineWidth:1,labelSize:11,formatTimeCallback:e=>`${Math.floor(e/60)}:${`0${Math.floor(e)%60}`.slice(-2)}`};class o extends t{constructor(e){super(e||{}),this.unsubscribe=()=>{},this.onPointerMove=e=>{if(!this.wavesurfer)return;const t=this.wavesurfer.getWrapper().getBoundingClientRect(),{width:s}=t,i=e.clientX-t.left,n=Math.min(1,Math.max(0,i/s)),o=Math.min(s-this.options.lineWidth-1,i);this.wrapper.style.transform=`translateX(${o}px)`,this.wrapper.style.opacity="1";const r=this.wavesurfer.getDuration()||0;this.label.textContent=this.options.formatTimeCallback(r*n);const a=this.label.offsetWidth;this.label.style.transform=o+a>s?`translateX(-${a+this.options.lineWidth}px)`:"",this.emit("hover",n)},this.onPointerLeave=()=>{this.wrapper.style.opacity="0"},this.options=Object.assign({},n,e),this.wrapper=i("div",{part:"hover"}),this.label=i("span",{part:"hover-label"},this.wrapper)}static create(e){return new o(e)}addUnits(e){return`${e}${"number"==typeof e?"px":""}`}onInit(){if(!this.wavesurfer)throw Error("WaveSurfer is not initialized");const e=this.wavesurfer.options,t=this.options.lineColor||e.cursorColor||e.progressColor;Object.assign(this.wrapper.style,{position:"absolute",zIndex:10,left:0,top:0,height:"100%",pointerEvents:"none",borderLeft:`${this.addUnits(this.options.lineWidth)} solid ${t}`,opacity:"0",transition:"opacity .1s ease-in"}),Object.assign(this.label.style,{display:"block",backgroundColor:this.options.labelBackground,color:this.options.labelColor,fontSize:`${this.addUnits(this.options.labelSize)}`,transition:"transform .1s ease-in",padding:"2px 3px"});const s=this.wavesurfer.getWrapper();s.appendChild(this.wrapper),s.addEventListener("pointermove",this.onPointerMove),s.addEventListener("pointerleave",this.onPointerLeave),s.addEventListener("wheel",this.onPointerMove),this.unsubscribe=()=>{s.removeEventListener("pointermove",this.onPointerMove),s.removeEventListener("pointerleave",this.onPointerLeave),s.removeEventListener("wheel",this.onPointerLeave)}}destroy(){super.destroy(),this.unsubscribe(),this.wrapper.remove()}}return o}));
