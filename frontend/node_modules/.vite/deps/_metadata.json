{"hash": "be2231a3", "configHash": "674045ef", "lockfileHash": "5bd98ec5", "browserHash": "45cdcbc1", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "6e36a6a5", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "98e72398", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "05ffb438", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "7071837f", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "b65e0dc0", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "d2a5e9e6", "needsInterop": false}, "wavesurfer.js": {"src": "../../wavesurfer.js/dist/wavesurfer.esm.js", "file": "wavesurfer__js.js", "fileHash": "c83f66ce", "needsInterop": false}}, "chunks": {"chunk-TYILIMWK": {"file": "chunk-TYILIMWK.js"}, "chunk-CANBAPAS": {"file": "chunk-CANBAPAS.js"}, "chunk-5WRI5ZAA": {"file": "chunk-5WRI5ZAA.js"}}}